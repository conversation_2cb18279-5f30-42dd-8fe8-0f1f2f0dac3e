import { Trash2, TrendingUp, Alert<PERSON>riangle, ExternalLink, RefreshCw, Activity, ArrowUp, ArrowDown } from 'lucide-react';
import { Stock } from '@/types';
import { useBatchStockData } from '@/hooks/useStockData';
import { detectVPattern } from '@/utils/patternDetection';
import { useMemo } from 'react';

interface StockListProps {
  stocks: Stock[];
  onRemoveStock: (code: string) => void;
  onSelectStock?: (code: string) => void;
  selectedStock?: string | null;
  /** 是否显示实时监控数据 */
  showRealTimeData?: boolean;
}

export function StockList({
  stocks,
  onRemoveStock,
  onSelectStock,
  selectedStock,
  showRealTimeData = false
}: StockListProps) {
  // 获取股票代码列表
  const stockCodes = stocks.map(s => s.code);

  // 批量获取股票数据（仅在显示实时数据时）
  const {
    results,
    isLoading: dataLoading,
    isFetching,
    refetch
  } = useBatchStockData(stockCodes, 20, {
    refetchInterval: showRealTimeData ? 60000 : false, // 60秒自动刷新
    enabled: showRealTimeData && stockCodes.length > 0
  });

  // 处理股票数据，集成V字型识别和实时数据
  const stocksWithData = useMemo(() => {
    if (!showRealTimeData || !results || Object.keys(results).length === 0) {
      return stocks.map(stock => ({ ...stock, data: null, hasVPattern: false, latestFlow: 0, change24h: 0 }));
    }

    return stocks.map(stock => {
      const data = results[stock.code];
      const hasVPattern = data?.klines ? detectVPattern(data.klines).hasVPattern : false;

      // 计算最新流入和变化
      const latestFlow = data?.klines?.[data.klines.length - 1]?.mainNetInflow || 0;
      const change24h = data?.klines && data.klines.length >= 2
        ? data.klines[data.klines.length - 1].mainNetInflow - data.klines[data.klines.length - 2].mainNetInflow
        : 0;

      return {
        ...stock,
        data,
        hasVPattern,
        latestFlow,
        change24h,
      };
    });
  }, [stocks, results, showRealTimeData]);
  if (stocks.length === 0) {
    return (
      <div className="text-center py-8">
        <TrendingUp className="w-12 h-12 text-gray-300 mx-auto mb-3" />
        <p className="text-gray-500 mb-2">暂无股票代码</p>
        <p className="text-sm text-gray-400">请添加股票代码开始监控</p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {/* 列表头部 */}
      <div className="flex items-center justify-between text-sm text-gray-600 pb-2 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <span>股票代码 ({stocks.length})</span>
          {showRealTimeData && (
            <>
              {isFetching && (
                <RefreshCw className="w-3 h-3 animate-spin text-blue-500" />
              )}
              <button
                onClick={() => refetch()}
                className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
                disabled={isFetching}
                title="刷新实时数据"
              >
                <Activity className="w-3 h-3" />
              </button>
            </>
          )}
        </div>
        <span>操作</span>
      </div>
      
      {/* 股票列表 */}
      <div className="space-y-1 max-h-64 overflow-y-auto">
        {stocksWithData.map((stock) => (
          <StockListItem
            key={stock.code}
            stock={stock}
            isSelected={selectedStock === stock.code}
            onSelect={onSelectStock}
            onRemove={onRemoveStock}
            showRealTimeData={showRealTimeData}
            stockData={stock.data}
            hasVPattern={stock.hasVPattern}
            latestFlow={stock.latestFlow}
            change24h={stock.change24h}
          />
        ))}
      </div>
      
      {/* 批量操作和状态信息 */}
      {stocks.length > 1 && (
        <div className="pt-3 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <button
              onClick={() => {
                if (window.confirm('确定要清空所有股票代码吗？')) {
                  stocks.forEach(stock => onRemoveStock(stock.code));
                }
              }}
              className="text-sm text-danger-600 hover:text-danger-700 flex items-center gap-1"
            >
              <AlertTriangle className="w-4 h-4" />
              清空所有
            </button>

            {showRealTimeData && (
              <div className="text-xs text-gray-500">
                {dataLoading ? '加载中...' : `实时监控已启用`}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

interface StockListItemProps {
  stock: Stock;
  isSelected?: boolean;
  onSelect?: (code: string) => void;
  onRemove: (code: string) => void;
  showRealTimeData?: boolean;
  stockData?: any;
  hasVPattern?: boolean;
  latestFlow?: number;
  change24h?: number;
}

function StockListItem({
  stock,
  isSelected,
  onSelect,
  onRemove,
  showRealTimeData = false,
  stockData,
  hasVPattern = false,
  latestFlow = 0,
  change24h = 0
}: StockListItemProps) {
  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm(`确定要删除股票 ${stock.code} 吗？`)) {
      onRemove(stock.code);
    }
  };

  const handleSelect = () => {
    if (onSelect) {
      onSelect(stock.code);
    }
  };

  const handleViewFlow = (e: React.MouseEvent) => {
    e.stopPropagation();
    const url = `https://data.eastmoney.com/zjlx/${stock.code}.html`;
    window.open(url, '_blank');
  };

  // 格式化资金流向数据
  const formatFlow = (value: number) => {
    if (Math.abs(value) >= 100000000) {
      return `${(value / 100000000).toFixed(2)}亿`;
    } else if (Math.abs(value) >= 10000) {
      return `${(value / 10000).toFixed(2)}万`;
    }
    return value.toFixed(2);
  };

  return (
    <div
      className={`
        flex items-center justify-between p-3 rounded-lg border transition-all duration-200
        ${isSelected
          ? 'border-primary-500 bg-primary-50'
          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
        }
        ${onSelect ? 'cursor-pointer' : ''}
      `}
      onClick={handleSelect}
    >
      <div className="flex items-center gap-3 flex-1">
        {/* 股票代码 */}
        <div className="flex flex-col">
          <span className={`text-sm font-medium ${
            isSelected ? 'text-primary-700' : 'text-gray-900'
          }`}>
            {stock.name}
          </span>
          <span className="text-xs text-gray-500 font-mono">
            {stock.code}
          </span>
        </div>

        {/* 选中指示器 */}
        {isSelected && (
          <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
        )}

        {/* 实时监控数据 */}
        {showRealTimeData && (
          <div className="ml-auto flex items-center gap-3">
            {/* V字形模式指示器 */}
            {hasVPattern && (
              <div className="px-1.5 py-0.5 bg-yellow-100 text-yellow-800 rounded text-xs font-medium">
                V形
              </div>
            )}

            {/* 资金流向数据 */}
            {stockData && (
              <div className="flex flex-col items-end">
                <div className={`text-sm font-medium ${
                  latestFlow > 0 ? 'text-success-600' : latestFlow < 0 ? 'text-danger-600' : 'text-gray-600'
                }`}>
                  {latestFlow > 0 ? '+' : ''}{formatFlow(latestFlow)}
                </div>
                <div className="flex items-center text-xs">
                  {change24h > 0 ? (
                    <ArrowUp className="w-3 h-3 text-success-500 mr-0.5" />
                  ) : change24h < 0 ? (
                    <ArrowDown className="w-3 h-3 text-danger-500 mr-0.5" />
                  ) : null}
                  <span className={`${
                    change24h > 0 ? 'text-success-600' : change24h < 0 ? 'text-danger-600' : 'text-gray-500'
                  }`}>
                    {change24h > 0 ? '+' : ''}{formatFlow(change24h)}
                  </span>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 操作按钮组 */}
      <div className="flex items-center gap-1">
        {/* 查看资金流向按钮 */}
        <button
          onClick={handleViewFlow}
          className="p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200"
          title="查看资金流向"
        >
          <ExternalLink className="w-4 h-4" />
        </button>

        {/* 删除按钮 */}
        <button
          onClick={handleRemove}
          className="p-1 text-gray-400 hover:text-danger-600 transition-colors duration-200"
          title="删除股票"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}
