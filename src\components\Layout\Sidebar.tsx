import React from 'react';
import { StockManager } from '@/components';
import { X } from 'lucide-react';

interface SidebarProps {
  /** 选中的股票代码 */
  selectedStock: string | null;
  /** 股票选择事件 */
  onStockSelect: (code: string) => void;
  /** 是否显示侧边栏（移动端） */
  isOpen?: boolean;
  /** 关闭侧边栏事件（移动端） */
  onClose?: () => void;
  /** 自定义类名 */
  className?: string;
}

/**
 * 侧边栏组件
 */
export const Sidebar: React.FC<SidebarProps> = ({
  selectedStock,
  onStockSelect,
  isOpen = true,
  onClose,
  className = '',
}) => {



  return (
    <>
      {/* 移动端遮罩层 */}
      {isOpen && onClose && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* 侧边栏内容 */}
      <aside
        className={`
          fixed lg:static inset-y-0 left-0 z-50 lg:z-auto
          w-96 bg-white border-r border-gray-200
          transform transition-transform duration-300 ease-in-out
          ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          ${className}
        `}
      >
        {/* 侧边栏头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 lg:hidden">
          <h2 className="text-lg font-semibold text-gray-900">股票管理</h2>
          {onClose && (
            <button
              onClick={onClose}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              aria-label="关闭菜单"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* 侧边栏内容 */}
        <div className="flex flex-col h-full lg:h-auto">
          {/* 股票管理组件 - 现在包含集成的实时监控功能 */}
          <div className="flex-1 p-4">
            <StockManager
              selectedStock={selectedStock}
              onSelectStock={onStockSelect}
            />
          </div>
        </div>
      </aside>
    </>
  );
};
